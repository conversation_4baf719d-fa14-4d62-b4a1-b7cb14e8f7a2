# Construction Estimate App - Phase 1 Setup Guide

## Overview
This guide will help you set up the Phase 1 implementation with real Supabase authentication and database integration.

## Prerequisites
- Node.js 18+ installed
- Expo CLI installed (`npm install -g @expo/cli`)
- A Supabase account and project

## Phase 1 Implementation Status

### ✅ Completed
1. **Real Supabase Authentication** - Replaced mock auth with Supabase Auth
2. **Database Integration** - Connected clients and estimates to real Supabase data
3. **Estimate Creation Screen** - Built comprehensive estimate creation wizard
4. **Client Management** - Added client creation and management forms
5. **Loading States** - Added proper loading and error handling
6. **Data Hooks** - Created reusable hooks for data fetching

### 🔧 Setup Required

#### 1. Supabase Configuration
1. Create a new Supabase project at https://supabase.com
2. Copy your project URL and anon key from Settings > API
3. Create a `.env` file in the root directory:
   ```
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

#### 2. Database Setup
The database schema is already defined in `supabase/migrations/`. To apply it:

1. Install Supabase CLI: `npm install -g supabase`
2. Login to Supabase: `supabase login`
3. Link your project: `supabase link --project-ref your-project-ref`
4. Push the schema: `supabase db push`

#### 3. Row Level Security (RLS)
The migration includes RLS policies, but verify they're enabled:
- Users can only see their own data
- Admins can see all data
- Proper authentication is required for all operations

#### 4. Test Data (Optional)
To add some test data for development:

```sql
-- Insert test users (run in Supabase SQL editor)
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
VALUES 
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), now(), now()),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), now(), now());

-- Insert user profiles
INSERT INTO users (id, email, first_name, last_name, role)
VALUES 
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'John', 'Smith', 'admin'),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>', 'Sarah', 'Johnson', 'manager');

-- Insert test clients
INSERT INTO clients (name, email, phone, address, city, state, zip_code, status)
VALUES 
  ('ABC Construction', '<EMAIL>', '(*************', '123 Main St', 'Austin', 'TX', '78701', 'active'),
  ('XYZ Builders', '<EMAIL>', '(*************', '456 Oak Ave', 'Dallas', 'TX', '75201', 'prospect');
```

## Running the App

1. Install dependencies: `npm install`
2. Start the development server: `npx expo start`
3. Use Expo Go app to scan QR code or run on simulator

## Key Features Implemented

### Authentication
- Real Supabase authentication with email/password
- User registration with profile creation
- Automatic session management
- Role-based access control

### Client Management
- Create new clients with full contact information
- View all clients with search and filtering
- Real-time data loading with proper error handling
- Status management (prospect, active, inactive)

### Estimate Creation
- Comprehensive estimate creation wizard
- Line item management with quantity, unit price, totals
- Tax calculation
- Client selection
- Template support (structure ready)
- Real-time total calculations

### Data Management
- Real Supabase database integration
- Proper error handling and loading states
- Optimistic updates where appropriate
- Data validation and constraints

## Next Steps for Phase 2

1. **Complete Detail Screens**
   - Individual estimate detail/edit screens
   - Client detail screens with estimate history
   - Template detail and editing

2. **PDF Generation**
   - Professional estimate PDFs
   - Email functionality
   - Branding customization

3. **Enhanced Features**
   - File attachments
   - Advanced search and filtering
   - Bulk operations
   - Estimate approval workflow

## Troubleshooting

### Common Issues

1. **Supabase Connection Errors**
   - Verify your `.env` file has correct credentials
   - Check that your Supabase project is active
   - Ensure RLS policies are properly configured

2. **Authentication Issues**
   - Make sure email confirmation is disabled in Supabase Auth settings for development
   - Check that user profiles are created in the `users` table

3. **Database Errors**
   - Verify the migration was applied successfully
   - Check that all required tables exist
   - Ensure RLS policies allow the operations you're trying to perform

### Development Tips

1. Use the Supabase dashboard to monitor database activity
2. Check the network tab in your browser/debugger for API calls
3. Enable debug logging in the Supabase client for detailed error information
4. Use the built-in error handling and loading states for better UX

## Support

If you encounter issues:
1. Check the console for error messages
2. Verify your Supabase configuration
3. Ensure all dependencies are installed correctly
4. Review the database schema and RLS policies

The app now has a solid foundation with real authentication and database integration. Phase 2 will focus on completing the user experience with detail screens, PDF generation, and enhanced features.
