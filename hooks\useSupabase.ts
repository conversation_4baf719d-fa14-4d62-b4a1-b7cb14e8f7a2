import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { Client, Estimate, Template, EstimateItem } from '@/types';

type Tables = Database['public']['Tables'];

export function useQuery<T extends keyof Tables>(
  table: T,
  options: {
    select?: string;
    eq?: { column: string; value: any };
    order?: { column: string; ascending?: boolean };
    limit?: number;
  } = {}
) {
  const [data, setData] = useState<Tables[T]['Row'][]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      let query = supabase.from(table).select(options.select || '*');

      if (options.eq) {
        query = query.eq(options.eq.column, options.eq.value);
      }

      if (options.order) {
        query = query.order(options.order.column, {
          ascending: options.order.ascending ?? true,
        });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data: result, error: queryError } = await query;

      if (queryError) throw queryError;
      setData(result || []);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [table, JSON.stringify(options)]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, error, loading, refetch: fetchData };
}

// Specialized hooks for our data types
export function useClients() {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchClients = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .order('name');

      if (error) throw error;

      const clientsData: Client[] = (data || []).map(client => ({
        id: client.id,
        name: client.name,
        email: client.email,
        phone: client.phone || '',
        address: client.address || '',
        city: client.city || '',
        state: client.state || '',
        zipCode: client.zip_code || '',
        status: client.status,
        notes: client.notes || '',
        createdAt: client.created_at || '',
        updatedAt: client.updated_at || '',
      }));

      setClients(clientsData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
      setClients([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  return { clients, loading, error, refetch: fetchClients };
}

export function useEstimates() {
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchEstimates = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('estimates')
        .select(`
          *,
          clients (name),
          estimate_items (*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const estimatesData: Estimate[] = (data || []).map(estimate => ({
        id: estimate.id,
        title: estimate.title,
        clientId: estimate.client_id || '',
        clientName: estimate.clients?.name || 'Unknown Client',
        status: estimate.status,
        items: estimate.estimate_items.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || '',
          quantity: item.quantity,
          unit: item.unit,
          unitPrice: item.unit_price,
          total: item.total,
        })),
        subtotal: estimate.subtotal,
        taxRate: estimate.tax_rate,
        taxAmount: estimate.tax_amount,
        total: estimate.total,
        notes: estimate.notes || '',
        createdBy: estimate.created_by || '',
        createdAt: estimate.created_at || '',
        updatedAt: estimate.updated_at || '',
        expiresAt: estimate.expires_at || '',
      }));

      setEstimates(estimatesData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load estimates');
      setEstimates([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEstimates();
  }, [fetchEstimates]);

  return { estimates, loading, error, refetch: fetchEstimates };
}

export function useMutation<T extends keyof Tables>(table: T) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const insert = async (data: Tables[T]['Insert']) => {
    setLoading(true);
    setError(null);
    try {
      const { data: result, error: insertError } = await supabase
        .from(table)
        .insert(data)
        .select()
        .single();

      if (insertError) throw insertError;
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const update = async (id: string, data: Tables[T]['Update']) => {
    setLoading(true);
    setError(null);
    try {
      const { data: result, error: updateError } = await supabase
        .from(table)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (updateError) throw updateError;
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const remove = async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const { error: deleteError } = await supabase
        .from(table)
        .delete()
        .eq('id', id);

      if (deleteError) throw deleteError;
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    insert,
    update,
    remove,
    loading,
    error,
  };
}