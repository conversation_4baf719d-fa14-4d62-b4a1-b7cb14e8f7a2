import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Alert,
  FlatList 
} from 'react-native';
import { router } from 'expo-router';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { TextField } from '@/components/TextField';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';
import { Client, EstimateItem, Template } from '@/types';
import { Plus, Trash2, Save, Users, FileText } from 'lucide-react-native';

interface EstimateFormData {
  title: string;
  clientId: string;
  notes: string;
  taxRate: number;
  items: EstimateItem[];
}

export default function CreateEstimateScreen() {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [showClientPicker, setShowClientPicker] = useState(false);
  const [showTemplatePicker, setShowTemplatePicker] = useState(false);
  
  const [formData, setFormData] = useState<EstimateFormData>({
    title: '',
    clientId: '',
    notes: '',
    taxRate: 8.25, // Default tax rate
    items: [],
  });

  // Load clients and templates
  useEffect(() => {
    loadClients();
    loadTemplates();
  }, []);

  const loadClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('status', 'active')
        .order('name');

      if (error) throw error;
      setClients(data || []);
    } catch (error) {
      console.error('Error loading clients:', error);
      Alert.alert('Error', 'Failed to load clients');
    }
  };

  const loadTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('templates')
        .select(`
          *,
          template_items (*)
        `)
        .order('name');

      if (error) throw error;
      
      // Convert to our Template type
      const templatesData: Template[] = (data || []).map(template => ({
        id: template.id,
        name: template.name,
        category: template.category,
        description: template.description || '',
        items: template.template_items.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || '',
          quantity: item.quantity,
          unit: item.unit,
          unitPrice: item.unit_price,
          total: item.total,
        })),
        createdBy: template.created_by || '',
        createdAt: template.created_at || '',
        updatedAt: template.updated_at || '',
      }));
      
      setTemplates(templatesData);
    } catch (error) {
      console.error('Error loading templates:', error);
      Alert.alert('Error', 'Failed to load templates');
    }
  };

  const addLineItem = () => {
    const newItem: EstimateItem = {
      id: `temp-${Date.now()}`,
      name: '',
      description: '',
      quantity: 1,
      unit: 'each',
      unitPrice: 0,
      total: 0,
    };
    
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  const updateLineItem = (index: number, field: keyof EstimateItem, value: any) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };
      
      // Recalculate total for this item
      if (field === 'quantity' || field === 'unitPrice') {
        newItems[index].total = newItems[index].quantity * newItems[index].unitPrice;
      }
      
      return { ...prev, items: newItems };
    });
  };

  const removeLineItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const applyTemplate = (template: Template) => {
    setFormData(prev => ({
      ...prev,
      items: template.items.map(item => ({
        ...item,
        id: `temp-${Date.now()}-${Math.random()}`,
      })),
    }));
    setShowTemplatePicker(false);
  };

  const calculateSubtotal = () => {
    return formData.items.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTaxAmount = () => {
    return calculateSubtotal() * (formData.taxRate / 100);
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTaxAmount();
  };

  const saveEstimate = async () => {
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter an estimate title');
      return;
    }
    
    if (!formData.clientId) {
      Alert.alert('Error', 'Please select a client');
      return;
    }
    
    if (formData.items.length === 0) {
      Alert.alert('Error', 'Please add at least one line item');
      return;
    }

    setIsLoading(true);
    
    try {
      const subtotal = calculateSubtotal();
      const taxAmount = calculateTaxAmount();
      const total = calculateTotal();

      // Create the estimate
      const { data: estimate, error: estimateError } = await supabase
        .from('estimates')
        .insert({
          title: formData.title,
          client_id: formData.clientId,
          status: 'draft',
          subtotal,
          tax_rate: formData.taxRate,
          tax_amount: taxAmount,
          total,
          notes: formData.notes,
          created_by: user?.id,
        })
        .select()
        .single();

      if (estimateError) throw estimateError;

      // Create the estimate items
      const itemsToInsert = formData.items.map(item => ({
        estimate_id: estimate.id,
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        unit: item.unit,
        unit_price: item.unitPrice,
        total: item.total,
      }));

      const { error: itemsError } = await supabase
        .from('estimate_items')
        .insert(itemsToInsert);

      if (itemsError) throw itemsError;

      Alert.alert('Success', 'Estimate created successfully', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error saving estimate:', error);
      Alert.alert('Error', 'Failed to save estimate');
    } finally {
      setIsLoading(false);
    }
  };

  const selectedClient = clients.find(c => c.id === formData.clientId);

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Basic Information</Text>
          
          <TextField
            label="Estimate Title"
            placeholder="Enter estimate title"
            value={formData.title}
            onChangeText={(value) => setFormData(prev => ({ ...prev, title: value }))}
          />

          <TouchableOpacity
            style={[styles.clientSelector, { borderColor: colors.border }]}
            onPress={() => setShowClientPicker(true)}
          >
            <Users size={20} color={colors.textSecondary} />
            <Text style={[styles.clientSelectorText, { color: selectedClient ? colors.text : colors.textSecondary }]}>
              {selectedClient ? selectedClient.name : 'Select Client'}
            </Text>
          </TouchableOpacity>

          <TextField
            label="Notes"
            placeholder="Additional notes (optional)"
            value={formData.notes}
            onChangeText={(value) => setFormData(prev => ({ ...prev, notes: value }))}
            multiline
            numberOfLines={3}
          />

          <TextField
            label="Tax Rate (%)"
            placeholder="8.25"
            value={formData.taxRate.toString()}
            onChangeText={(value) => setFormData(prev => ({ ...prev, taxRate: parseFloat(value) || 0 }))}
            keyboardType="numeric"
          />
        </Card>

        {/* Line Items */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Line Items</Text>
            <View style={styles.sectionActions}>
              <Button
                title="Template"
                onPress={() => setShowTemplatePicker(true)}
                variant="outline"
                size="sm"
                leftIcon={<FileText size={16} color={colors.primary} />}
              />
              <Button
                title="Add Item"
                onPress={addLineItem}
                variant="primary"
                size="sm"
                leftIcon={<Plus size={16} color="#FFFFFF" />}
              />
            </View>
          </View>

          {formData.items.map((item, index) => (
            <View key={item.id} style={[styles.lineItem, { borderColor: colors.border }]}>
              <View style={styles.lineItemHeader}>
                <Text style={[styles.lineItemTitle, { color: colors.text }]}>Item {index + 1}</Text>
                <TouchableOpacity onPress={() => removeLineItem(index)}>
                  <Trash2 size={20} color={colors.error} />
                </TouchableOpacity>
              </View>

              <TextField
                label="Name"
                placeholder="Item name"
                value={item.name}
                onChangeText={(value) => updateLineItem(index, 'name', value)}
              />

              <TextField
                label="Description"
                placeholder="Item description"
                value={item.description}
                onChangeText={(value) => updateLineItem(index, 'description', value)}
                multiline
                numberOfLines={2}
              />

              <View style={styles.lineItemRow}>
                <View style={styles.lineItemField}>
                  <TextField
                    label="Quantity"
                    placeholder="1"
                    value={item.quantity.toString()}
                    onChangeText={(value) => updateLineItem(index, 'quantity', parseFloat(value) || 0)}
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.lineItemField}>
                  <TextField
                    label="Unit"
                    placeholder="each"
                    value={item.unit}
                    onChangeText={(value) => updateLineItem(index, 'unit', value)}
                  />
                </View>

                <View style={styles.lineItemField}>
                  <TextField
                    label="Unit Price"
                    placeholder="0.00"
                    value={item.unitPrice.toString()}
                    onChangeText={(value) => updateLineItem(index, 'unitPrice', parseFloat(value) || 0)}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <View style={styles.lineItemTotal}>
                <Text style={[styles.lineItemTotalLabel, { color: colors.textSecondary }]}>Total:</Text>
                <Text style={[styles.lineItemTotalValue, { color: colors.text }]}>
                  ${item.total.toFixed(2)}
                </Text>
              </View>
            </View>
          ))}

          {formData.items.length === 0 && (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                No line items added yet. Click "Add Item" to get started.
              </Text>
            </View>
          )}
        </Card>

        {/* Summary */}
        {formData.items.length > 0 && (
          <Card style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Summary</Text>
            
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Subtotal:</Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                ${calculateSubtotal().toFixed(2)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Tax ({formData.taxRate}%):
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                ${calculateTaxAmount().toFixed(2)}
              </Text>
            </View>

            <View style={[styles.summaryRow, styles.summaryTotal, { borderTopColor: colors.border }]}>
              <Text style={[styles.summaryTotalLabel, { color: colors.text }]}>Total:</Text>
              <Text style={[styles.summaryTotalValue, { color: colors.primary }]}>
                ${calculateTotal().toFixed(2)}
              </Text>
            </View>
          </Card>
        )}

        {/* Save Button */}
        <Button
          title="Save Estimate"
          onPress={saveEstimate}
          variant="primary"
          size="lg"
          loading={isLoading}
          leftIcon={<Save size={20} color="#FFFFFF" />}
          style={styles.saveButton}
        />
      </View>

      {/* Client Picker Modal would go here */}
      {/* Template Picker Modal would go here */}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionActions: {
    flexDirection: 'row',
    gap: 8,
  },
  clientSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
    gap: 12,
  },
  clientSelectorText: {
    fontSize: 16,
    flex: 1,
  },
  lineItem: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  lineItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  lineItemTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  lineItemRow: {
    flexDirection: 'row',
    gap: 12,
  },
  lineItemField: {
    flex: 1,
  },
  lineItemTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  lineItemTotalLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  lineItemTotalValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryTotal: {
    borderTopWidth: 1,
    paddingTop: 12,
    marginTop: 8,
  },
  summaryTotalLabel: {
    fontSize: 18,
    fontWeight: '600',
  },
  summaryTotalValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  saveButton: {
    marginTop: 24,
    marginBottom: 32,
  },
});
