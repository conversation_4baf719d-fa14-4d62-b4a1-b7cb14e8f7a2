import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SupabaseTest } from '@/components/SupabaseTest';
import { useTheme } from '@/context/ThemeContext';

export default function TestScreen() {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <SupabaseTest />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
