#!/usr/bin/env node

/**
 * Supabase Connection Test Script
 * 
 * This script tests the Supabase connection and basic functionality
 * Run with: node scripts/test-supabase.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testSupabaseConnection() {
  log('\n' + '='.repeat(50), colors.bold);
  log('🧪 SUPABASE CONNECTION TEST', colors.bold);
  log('='.repeat(50), colors.bold);

  // Check environment variables
  log('\n📋 Checking Environment Variables...', colors.bold);
  
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    error('EXPO_PUBLIC_SUPABASE_URL is not set in .env file');
    return false;
  } else {
    success(`Supabase URL: ${supabaseUrl}`);
  }

  if (!supabaseKey) {
    error('EXPO_PUBLIC_SUPABASE_ANON_KEY is not set in .env file');
    return false;
  } else {
    success(`Supabase Key: ${supabaseKey.substring(0, 20)}...`);
  }

  // Initialize Supabase client
  log('\n🔌 Initializing Supabase Client...', colors.bold);
  
  let supabase;
  try {
    supabase = createClient(supabaseUrl, supabaseKey);
    success('Supabase client initialized successfully');
  } catch (err) {
    error(`Failed to initialize Supabase client: ${err.message}`);
    return false;
  }

  // Test basic connection
  log('\n🌐 Testing Basic Connection...', colors.bold);
  
  try {
    const { data, error: healthError } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });
    
    if (healthError) {
      error(`Connection test failed: ${healthError.message}`);
      return false;
    }
    
    success('Basic connection test passed');
  } catch (err) {
    error(`Connection test failed: ${err.message}`);
    return false;
  }

  // Test table access
  log('\n📊 Testing Table Access...', colors.bold);
  
  const tables = ['users', 'clients', 'estimates', 'estimate_items', 'templates', 'template_items'];
  
  for (const table of tables) {
    try {
      const { data, error: tableError } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (tableError) {
        error(`Table '${table}': ${tableError.message}`);
      } else {
        success(`Table '${table}': Accessible`);
      }
    } catch (err) {
      error(`Table '${table}': ${err.message}`);
    }
  }

  // Test authentication
  log('\n🔐 Testing Authentication...', colors.bold);
  
  try {
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      warning(`Auth session check: ${sessionError.message}`);
    } else if (session) {
      success(`Current session: User ${session.user.email} is logged in`);
    } else {
      info('No active session (this is normal for testing)');
    }
  } catch (err) {
    warning(`Auth test failed: ${err.message}`);
  }

  // Test RLS policies (basic check)
  log('\n🛡️  Testing Row Level Security...', colors.bold);
  
  try {
    // Try to access clients without authentication (should be restricted)
    const { data, error: rlsError } = await supabase
      .from('clients')
      .select('*')
      .limit(1);
    
    if (rlsError && rlsError.code === 'PGRST116') {
      success('RLS is properly configured (access denied without auth)');
    } else if (data) {
      warning('RLS might not be properly configured (data accessible without auth)');
    } else {
      info('RLS test inconclusive');
    }
  } catch (err) {
    warning(`RLS test failed: ${err.message}`);
  }

  log('\n' + '='.repeat(50), colors.bold);
  log('🎉 SUPABASE TEST COMPLETE', colors.bold);
  log('='.repeat(50), colors.bold);

  return true;
}

// Test data insertion (optional)
async function testDataOperations() {
  log('\n🧪 Testing Data Operations (Optional)...', colors.bold);
  
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Note: These operations will likely fail due to RLS policies
  // This is expected behavior and shows that security is working
  
  try {
    const { data, error } = await supabase
      .from('clients')
      .insert({
        name: 'Test Client',
        email: '<EMAIL>',
        phone: '555-0123',
        status: 'prospect'
      })
      .select();

    if (error) {
      if (error.code === 'PGRST116' || error.message.includes('RLS')) {
        success('Data insertion properly blocked by RLS (this is good!)');
      } else {
        warning(`Data insertion failed: ${error.message}`);
      }
    } else {
      warning('Data insertion succeeded without authentication (check RLS policies)');
    }
  } catch (err) {
    warning(`Data operation test failed: ${err.message}`);
  }
}

// Main execution
async function main() {
  try {
    const connectionSuccess = await testSupabaseConnection();
    
    if (connectionSuccess) {
      await testDataOperations();
      
      log('\n📝 Next Steps:', colors.bold);
      log('1. If all tests passed, your Supabase is configured correctly!');
      log('2. Run your Expo app: npx expo start');
      log('3. Try registering a new user to test authentication');
      log('4. Create some clients and estimates to test the full flow');
      log('\n💡 Tips:', colors.bold);
      log('- RLS errors are expected and show security is working');
      log('- Check the Supabase dashboard for real-time monitoring');
      log('- Enable email confirmation in Auth settings for production');
    } else {
      log('\n🔧 Troubleshooting:', colors.bold);
      log('1. Check your .env file has the correct Supabase credentials');
      log('2. Verify your Supabase project is active');
      log('3. Run: supabase db push (to apply migrations)');
      log('4. Check the Supabase dashboard for any issues');
    }
  } catch (err) {
    error(`Test script failed: ${err.message}`);
    process.exit(1);
  }
}

// Run the tests
main();
