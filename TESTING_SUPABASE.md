# Testing Supabase Integration

This guide provides multiple ways to test your Supabase integration and ensure everything is working correctly.

## Prerequisites

1. **Environment Setup**: Make sure you have a `.env` file with your Supabase credentials:
   ```
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. **Dependencies**: Install the required dependencies:
   ```bash
   npm install
   ```

3. **Database Schema**: Ensure your database schema is applied:
   ```bash
   supabase db push
   ```

## Testing Methods

### 1. Command Line Test (Recommended First Step)

Run the automated test script from your terminal:

```bash
npm run test-supabase
```

This will test:
- ✅ Environment variables
- ✅ Basic connection to Supabase
- ✅ Table access permissions
- ✅ Authentication system
- ✅ Row Level Security (RLS) policies

**Expected Output:**
```
🧪 SUPABASE CONNECTION TEST
==================================================
📋 Checking Environment Variables...
✅ Supabase URL: https://your-project.supabase.co
✅ Supabase Key: eyJhbGciOiJIUzI1NiI...

🔌 Initializing Supabase Client...
✅ Supabase client initialized successfully

🌐 Testing Basic Connection...
✅ Basic connection test passed

📊 Testing Table Access...
✅ Table 'users': Accessible
✅ Table 'clients': Accessible
✅ Table 'estimates': Accessible
✅ Table 'estimate_items': Accessible
✅ Table 'templates': Accessible
✅ Table 'template_items': Accessible

🔐 Testing Authentication...
ℹ️ No active session (this is normal for testing)

🛡️ Testing Row Level Security...
✅ RLS is properly configured (access denied without auth)

🎉 SUPABASE TEST COMPLETE
==================================================
```

### 2. In-App Test (Visual Testing)

1. **Start your Expo app**:
   ```bash
   npm run dev
   ```

2. **Navigate to Settings**:
   - Open your app
   - Go to the Settings tab
   - Tap "Test Supabase Connection" under Developer section

3. **Review Test Results**:
   - The test will run automatically
   - You'll see visual indicators for each test
   - Tap on any test for detailed error information

### 3. Manual Testing Through App Features

#### Test Authentication:
1. **Register a new user**:
   - Go to the login screen
   - Tap "Create Account"
   - Fill in user details
   - Submit registration

2. **Login with existing user**:
   - Use the credentials you just created
   - Verify you're redirected to the dashboard

#### Test Client Management:
1. **Create a client**:
   - Go to Clients tab
   - Tap "Add Client"
   - Fill in client information
   - Save the client

2. **View clients list**:
   - Verify the client appears in the list
   - Test search functionality
   - Test status filtering

#### Test Estimate Creation:
1. **Create an estimate**:
   - Go to Estimates tab
   - Tap "New"
   - Fill in estimate details
   - Add line items
   - Save the estimate

2. **View estimates list**:
   - Verify the estimate appears
   - Check calculations are correct
   - Test filtering options

## Troubleshooting Common Issues

### ❌ Environment Variables Not Found
**Problem**: `EXPO_PUBLIC_SUPABASE_URL is not set in .env file`

**Solution**:
1. Create a `.env` file in your project root
2. Add your Supabase credentials:
   ```
   EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   ```
3. Restart your development server

### ❌ Connection Failed
**Problem**: `Connection test failed: fetch failed`

**Solutions**:
1. **Check your internet connection**
2. **Verify Supabase project is active**:
   - Go to your Supabase dashboard
   - Ensure the project isn't paused
3. **Check URL format**:
   - Should be: `https://your-project-id.supabase.co`
   - No trailing slash

### ❌ Table Access Issues
**Problem**: `Table 'clients': relation "public.clients" does not exist`

**Solutions**:
1. **Apply database migrations**:
   ```bash
   supabase db push
   ```
2. **Check Supabase dashboard**:
   - Go to Table Editor
   - Verify all tables exist
3. **Reset database** (if needed):
   ```bash
   supabase db reset
   ```

### ⚠️ RLS Policy Warnings
**Problem**: `Data accessible without auth - check RLS policies`

**Solutions**:
1. **Enable RLS on tables**:
   ```sql
   ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
   ALTER TABLE estimates ENABLE ROW LEVEL SECURITY;
   ```
2. **Check existing policies**:
   - Go to Supabase Dashboard > Authentication > Policies
   - Ensure policies exist for each table
3. **Re-apply migrations**:
   ```bash
   supabase db push
   ```

### ❌ Authentication Issues
**Problem**: Registration or login fails

**Solutions**:
1. **Check Auth settings**:
   - Go to Supabase Dashboard > Authentication > Settings
   - Disable email confirmation for development
   - Check allowed domains
2. **Verify user table**:
   - Ensure `users` table exists
   - Check if user profiles are being created

## Expected Test Results

### ✅ All Tests Pass
- Environment variables configured
- Connection to Supabase successful
- All tables accessible
- RLS policies working correctly
- Authentication system functional

**Next Steps**: Your Supabase integration is ready! You can now:
- Register users and test authentication
- Create clients and estimates
- Test all app functionality

### ⚠️ Some Warnings
- Basic connection works
- Some RLS warnings (expected in development)
- Authentication might need configuration

**Next Steps**: 
- Review RLS policies if needed
- Configure authentication settings
- Test core functionality

### ❌ Critical Errors
- Environment variables missing
- Connection failures
- Missing database tables

**Next Steps**:
- Fix environment configuration
- Apply database migrations
- Check Supabase project status

## Development Tips

1. **Monitor in Real-time**:
   - Keep Supabase Dashboard open
   - Watch the "Logs" section for real-time activity
   - Monitor API usage

2. **Test Data**:
   - Use the SQL Editor to insert test data
   - Create sample clients and estimates
   - Test with different user roles

3. **Security Testing**:
   - Try accessing data without authentication
   - Test with different user accounts
   - Verify users can only see their own data

4. **Performance Testing**:
   - Test with larger datasets
   - Monitor query performance
   - Check loading states in the app

## Getting Help

If tests continue to fail:

1. **Check the Supabase Dashboard** for any error messages
2. **Review the console logs** in your development environment
3. **Verify your project settings** in Supabase
4. **Check the network tab** in your browser/debugger for failed requests

The testing tools provided will help you identify and resolve most common issues with your Supabase integration.
