import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/lib/supabase';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react-native';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
  details?: string;
}

export function SupabaseTest() {
  const { colors } = useTheme();
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (name: string, status: TestResult['status'], message: string, details?: string) => {
    setTests(prev => {
      const existing = prev.find(t => t.name === name);
      const newTest = { name, status, message, details };
      
      if (existing) {
        return prev.map(t => t.name === name ? newTest : t);
      } else {
        return [...prev, newTest];
      }
    });
  };

  const runTests = async () => {
    setIsRunning(true);
    setTests([]);

    // Test 1: Environment Variables
    updateTest('Environment', 'pending', 'Checking environment variables...');
    
    const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      updateTest(
        'Environment', 
        'error', 
        'Missing environment variables',
        'EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY must be set in .env file'
      );
      setIsRunning(false);
      return;
    }

    updateTest('Environment', 'success', 'Environment variables found');

    // Test 2: Basic Connection
    updateTest('Connection', 'pending', 'Testing basic connection...');
    
    try {
      const { data, error } = await supabase
        .from('users')
        .select('count', { count: 'exact', head: true });

      if (error) {
        updateTest('Connection', 'error', 'Connection failed', error.message);
      } else {
        updateTest('Connection', 'success', 'Connected to Supabase successfully');
      }
    } catch (err) {
      updateTest('Connection', 'error', 'Connection failed', (err as Error).message);
    }

    // Test 3: Table Access
    updateTest('Tables', 'pending', 'Testing table access...');
    
    const tables = ['users', 'clients', 'estimates', 'estimate_items'];
    let tableErrors = 0;
    
    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          tableErrors++;
        }
      } catch (err) {
        tableErrors++;
      }
    }

    if (tableErrors === 0) {
      updateTest('Tables', 'success', `All ${tables.length} tables accessible`);
    } else {
      updateTest('Tables', 'warning', `${tableErrors}/${tables.length} tables had issues`);
    }

    // Test 4: Authentication
    updateTest('Authentication', 'pending', 'Testing authentication...');
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        updateTest('Authentication', 'warning', 'Auth check failed', error.message);
      } else if (session) {
        updateTest('Authentication', 'success', `Logged in as ${session.user.email}`);
      } else {
        updateTest('Authentication', 'success', 'No active session (normal for testing)');
      }
    } catch (err) {
      updateTest('Authentication', 'warning', 'Auth test failed', (err as Error).message);
    }

    // Test 5: RLS Policies
    updateTest('Security', 'pending', 'Testing Row Level Security...');
    
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .limit(1);

      if (error && (error.code === 'PGRST116' || error.message.includes('RLS'))) {
        updateTest('Security', 'success', 'RLS policies are working correctly');
      } else if (data) {
        updateTest('Security', 'warning', 'Data accessible without auth - check RLS policies');
      } else {
        updateTest('Security', 'success', 'Security test passed');
      }
    } catch (err) {
      updateTest('Security', 'warning', 'Security test inconclusive', (err as Error).message);
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle size={20} color={colors.success} />;
      case 'error':
        return <XCircle size={20} color={colors.error} />;
      case 'warning':
        return <AlertCircle size={20} color={colors.warning} />;
      default:
        return <RefreshCw size={20} color={colors.textSecondary} />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return colors.success;
      case 'error':
        return colors.error;
      case 'warning':
        return colors.warning;
      default:
        return colors.textSecondary;
    }
  };

  const showTestDetails = (test: TestResult) => {
    if (test.details) {
      Alert.alert(test.name, test.details);
    }
  };

  useEffect(() => {
    runTests();
  }, []);

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <Card style={styles.headerCard}>
        <Text style={[styles.title, { color: colors.text }]}>
          Supabase Connection Test
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Testing your Supabase integration
        </Text>
        
        <Button
          title="Run Tests Again"
          onPress={runTests}
          variant="outline"
          loading={isRunning}
          leftIcon={<RefreshCw size={16} color={colors.primary} />}
          style={styles.runButton}
        />
      </Card>

      {tests.map((test, index) => (
        <Card key={test.name} style={styles.testCard}>
          <TouchableOpacity
            style={styles.testItem}
            onPress={() => showTestDetails(test)}
            disabled={!test.details}
          >
            <View style={styles.testHeader}>
              {getStatusIcon(test.status)}
              <Text style={[styles.testName, { color: colors.text }]}>
                {test.name}
              </Text>
            </View>
            
            <Text style={[styles.testMessage, { color: getStatusColor(test.status) }]}>
              {test.message}
            </Text>
            
            {test.details && (
              <Text style={[styles.tapHint, { color: colors.textSecondary }]}>
                Tap for details
              </Text>
            )}
          </TouchableOpacity>
        </Card>
      ))}

      {tests.length > 0 && !isRunning && (
        <Card style={styles.summaryCard}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            Test Summary
          </Text>
          
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.success }]}>
                {tests.filter(t => t.status === 'success').length}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Passed
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.warning }]}>
                {tests.filter(t => t.status === 'warning').length}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Warnings
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.error }]}>
                {tests.filter(t => t.status === 'error').length}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Failed
              </Text>
            </View>
          </View>

          <Text style={[styles.nextSteps, { color: colors.textSecondary }]}>
            {tests.some(t => t.status === 'error') 
              ? 'Fix the errors above before proceeding'
              : 'Your Supabase integration looks good! Try creating a user account.'
            }
          </Text>
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  headerCard: {
    marginBottom: 16,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  runButton: {
    minWidth: 150,
  },
  testCard: {
    marginBottom: 12,
    padding: 16,
  },
  testItem: {
    gap: 8,
  },
  testHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  testName: {
    fontSize: 18,
    fontWeight: '600',
  },
  testMessage: {
    fontSize: 14,
    marginLeft: 32,
  },
  tapHint: {
    fontSize: 12,
    fontStyle: 'italic',
    marginLeft: 32,
  },
  summaryCard: {
    marginTop: 16,
    padding: 20,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 14,
    marginTop: 4,
  },
  nextSteps: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
