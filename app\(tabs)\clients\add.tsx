import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Alert 
} from 'react-native';
import { router } from 'expo-router';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { TextField } from '@/components/TextField';
import { useTheme } from '@/context/ThemeContext';
import { supabase } from '@/lib/supabase';
import { Save, X } from 'lucide-react-native';

interface ClientFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  status: 'active' | 'inactive' | 'prospect';
  notes: string;
}

export default function AddClientScreen() {
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<ClientFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    status: 'prospect',
    notes: '',
  });

  const [errors, setErrors] = useState<Partial<ClientFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<ClientFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Company name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const saveClient = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const { data, error } = await supabase
        .from('clients')
        .insert({
          name: formData.name.trim(),
          email: formData.email.trim().toLowerCase(),
          phone: formData.phone.trim(),
          address: formData.address.trim(),
          city: formData.city.trim(),
          state: formData.state.trim(),
          zip_code: formData.zipCode.trim(),
          status: formData.status,
          notes: formData.notes.trim(),
        })
        .select()
        .single();

      if (error) throw error;

      Alert.alert('Success', 'Client added successfully', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error: any) {
      console.error('Error saving client:', error);
      
      if (error.code === '23505') {
        // Unique constraint violation (email already exists)
        Alert.alert('Error', 'A client with this email already exists');
      } else {
        Alert.alert('Error', 'Failed to save client. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const updateFormData = (field: keyof ClientFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Basic Information</Text>
          
          <TextField
            label="Company Name *"
            placeholder="Enter company name"
            value={formData.name}
            onChangeText={(value) => updateFormData('name', value)}
            error={errors.name}
          />

          <TextField
            label="Email Address *"
            placeholder="<EMAIL>"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
          />

          <TextField
            label="Phone Number *"
            placeholder="(*************"
            value={formData.phone}
            onChangeText={(value) => updateFormData('phone', value)}
            keyboardType="phone-pad"
            error={errors.phone}
          />
        </Card>

        {/* Address Information */}
        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Address Information</Text>
          
          <TextField
            label="Street Address"
            placeholder="123 Main Street"
            value={formData.address}
            onChangeText={(value) => updateFormData('address', value)}
          />

          <View style={styles.row}>
            <View style={styles.rowField}>
              <TextField
                label="City"
                placeholder="City"
                value={formData.city}
                onChangeText={(value) => updateFormData('city', value)}
              />
            </View>
            
            <View style={styles.rowFieldSmall}>
              <TextField
                label="State"
                placeholder="TX"
                value={formData.state}
                onChangeText={(value) => updateFormData('state', value)}
                autoCapitalize="characters"
              />
            </View>
          </View>

          <TextField
            label="ZIP Code"
            placeholder="12345"
            value={formData.zipCode}
            onChangeText={(value) => updateFormData('zipCode', value)}
            keyboardType="numeric"
          />
        </Card>

        {/* Additional Information */}
        <Card style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Additional Information</Text>
          
          <View style={styles.statusContainer}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Status</Text>
            <View style={styles.statusButtons}>
              {(['prospect', 'active', 'inactive'] as const).map((status) => (
                <Button
                  key={status}
                  title={status.charAt(0).toUpperCase() + status.slice(1)}
                  onPress={() => updateFormData('status', status)}
                  variant={formData.status === status ? 'primary' : 'outline'}
                  size="sm"
                  style={styles.statusButton}
                />
              ))}
            </View>
          </View>

          <TextField
            label="Notes"
            placeholder="Additional notes about this client..."
            value={formData.notes}
            onChangeText={(value) => updateFormData('notes', value)}
            multiline
            numberOfLines={4}
          />
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Cancel"
            onPress={() => router.back()}
            variant="outline"
            size="lg"
            leftIcon={<X size={20} color={colors.primary} />}
            style={styles.cancelButton}
          />
          
          <Button
            title="Save Client"
            onPress={saveClient}
            variant="primary"
            size="lg"
            loading={isLoading}
            leftIcon={<Save size={20} color="#FFFFFF" />}
            style={styles.saveButton}
          />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  rowField: {
    flex: 2,
  },
  rowFieldSmall: {
    flex: 1,
  },
  statusContainer: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  statusButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  statusButton: {
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 2,
  },
});
