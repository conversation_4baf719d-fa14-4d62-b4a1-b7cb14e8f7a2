import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Card } from '@/components/Card';
import { StatCard } from '@/components/StatCard';
import { StatusBadge } from '@/components/StatusBadge';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { useClients, useEstimates } from '@/hooks/useSupabase';
import { router } from 'expo-router';
import { FileText, Users, Clock, CircleCheck as CheckCircle, Calendar, Bell } from 'lucide-react-native';

export default function DashboardScreen() {
  const { colors } = useTheme();
  const { user } = useAuth();
  const { clients } = useClients();
  const { estimates } = useEstimates();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Calculate dashboard stats from real data
  const totalEstimates = estimates.length;
  const pendingApprovals = estimates.filter(e => e.status === 'pending').length;
  const activeClients = clients.filter(c => c.status === 'active').length;

  // Get recent estimates (last 3)
  const recentEstimates = estimates.slice(0, 3);

  // Get recent clients (last 3)
  const recentClients = clients.slice(0, 3);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'estimate_created':
        return <FileText size={16} color={colors.primary} />;
      case 'estimate_approved':
        return <CheckCircle size={16} color={colors.success} />;
      case 'estimate_declined':
        return <Clock size={16} color={colors.error} />;
      case 'client_added':
        return <Users size={16} color={colors.secondary} />;
      default:
        return <Bell size={16} color={colors.textSecondary} />;
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.welcomeSection}>
        <Text style={[styles.welcomeText, { color: colors.text }]}>
          Welcome back, {user?.firstName || 'User'}!
        </Text>
        <Text style={[styles.dateText, { color: colors.textSecondary }]}>
          {new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
        </Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.statsContainer}
        style={styles.statsScrollView}
      >
        <StatCard
          title="Total Estimates"
          value={totalEstimates.toString()}
          icon={<FileText size={20} color={colors.primary} />}
          style={{ marginRight: 12 }}
        />
        <StatCard
          title="Pending Approvals"
          value={pendingApprovals.toString()}
          icon={<Clock size={20} color={colors.warning} />}
          style={{ marginRight: 12 }}
        />
        <StatCard
          title="Active Clients"
          value={activeClients.toString()}
          icon={<Users size={20} color={colors.secondary} />}
        />
      </ScrollView>

      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Estimates</Text>
        <TouchableOpacity onPress={() => router.push('/(tabs)/estimates')}>
          <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
        </TouchableOpacity>
      </View>

      {recentEstimates.length > 0 ? (
        recentEstimates.map((estimate) => (
          <Card key={estimate.id} style={styles.estimateCard} onPress={() => {}}>
            <View style={styles.estimateHeader}>
              <Text style={[styles.estimateTitle, { color: colors.text }]} numberOfLines={1}>
                {estimate.title}
              </Text>
              <StatusBadge status={estimate.status} />
            </View>

            <View style={styles.estimateDetails}>
              <View style={styles.estimateDetail}>
                <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Client:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>{estimate.clientName}</Text>
              </View>
              <View style={styles.estimateDetail}>
                <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Total:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  ${estimate.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </Text>
              </View>
              <View style={styles.estimateDetail}>
                <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Created:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {formatDate(estimate.createdAt)}
                </Text>
              </View>
            </View>
          </Card>
        ))
      ) : (
        <Card style={styles.estimateCard}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No estimates yet. Create your first estimate to get started!
          </Text>
        </Card>
      )}

      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Clients</Text>
        <TouchableOpacity onPress={() => router.push('/(tabs)/clients')}>
          <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
        </TouchableOpacity>
      </View>

      {recentClients.length > 0 ? (
        recentClients.map((client) => (
          <Card key={client.id} style={styles.clientCard} onPress={() => {}}>
            <View style={styles.clientHeader}>
              <Text style={[styles.clientName, { color: colors.text }]}>{client.name}</Text>
              <StatusBadge status={client.status} />
            </View>

            <View style={styles.clientDetails}>
              <Text style={[styles.clientDetail, { color: colors.textSecondary }]}>
                {client.email} • {client.phone}
              </Text>
              <Text style={[styles.clientDetail, { color: colors.textSecondary }]}>
                {client.city}, {client.state}
              </Text>
            </View>
          </Card>
        ))
      ) : (
        <Card style={styles.clientCard}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No clients yet. Add your first client to get started!
          </Text>
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  welcomeSection: {
    marginBottom: 24,
  },
  welcomeText: {
    fontFamily: 'Inter-Bold',
    fontSize: 24,
  },
  dateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginTop: 4,
  },
  statsScrollView: {
    marginBottom: 24,
  },
  statsContainer: {
    paddingRight: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
  },
  viewAllText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  estimateCard: {
    marginBottom: 12,
  },
  estimateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  estimateTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    flex: 1,
    marginRight: 8,
  },
  estimateDetails: {
    gap: 4,
  },
  estimateDetail: {
    flexDirection: 'row',
  },
  detailLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginRight: 4,
  },
  detailValue: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  emptyText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    textAlign: 'center',
    padding: 16,
  },
  clientCard: {
    marginBottom: 12,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  clientName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  clientDetails: {
    gap: 4,
  },
  clientDetail: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
});