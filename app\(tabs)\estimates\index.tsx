import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { Card } from '@/components/Card';
import { StatusBadge } from '@/components/StatusBadge';
import { Button } from '@/components/Button';
import { useTheme } from '@/context/ThemeContext';
import { useEstimates } from '@/hooks/useSupabase';
import { Estimate } from '@/types';
import { Plus, Search, Calendar, RefreshCw, DollarSign } from 'lucide-react-native';

export default function EstimatesScreen() {
  const { colors } = useTheme();
  const { estimates, loading, error, refetch } = useEstimates();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | null>(null);

  const filteredEstimates = estimates.filter(estimate => {
    const matchesSearch = estimate.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          estimate.clientName.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus ? estimate.status === filterStatus : true;
    return matchesSearch && matchesFilter;
  });

  const filterOptions = [
    { label: 'All', value: null },
    { label: 'Draft', value: 'draft' },
    { label: 'Sent', value: 'sent' },
    { label: 'Approved', value: 'approved' },
    { label: 'Declined', value: 'declined' },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const renderEstimateItem = ({ item }: { item: Estimate }) => (
    <Card
      style={styles.estimateCard}
      onPress={() => router.push(`/estimates/${item.id}`)}
    >
      <View style={styles.estimateHeader}>
        <Text style={[styles.estimateTitle, { color: colors.text }]}>{item.title}</Text>
        <StatusBadge status={item.status} />
      </View>

      <View style={styles.clientSection}>
        <Text style={[styles.sectionLabel, { color: colors.textSecondary }]}>Client</Text>
        <Text style={[styles.clientName, { color: colors.text }]}>{item.clientName}</Text>
      </View>

      <View style={styles.rowContainer}>
        <View style={styles.infoColumn}>
          <Text style={[styles.sectionLabel, { color: colors.textSecondary }]}>Total</Text>
          <Text style={[styles.infoValue, { color: colors.text }]}>{formatCurrency(item.total)}</Text>
        </View>

        <View style={styles.infoColumn}>
          <Text style={[styles.sectionLabel, { color: colors.textSecondary }]}>Created</Text>
          <View style={styles.dateContainer}>
            <Calendar size={14} color={colors.textSecondary} style={styles.dateIcon} />
            <Text style={[styles.infoValue, { color: colors.text }]}>{formatDate(item.createdAt)}</Text>
          </View>
        </View>

        <View style={styles.infoColumn}>
          <Text style={[styles.sectionLabel, { color: colors.textSecondary }]}>Expires</Text>
          <View style={styles.dateContainer}>
            <Calendar size={14} color={colors.textSecondary} style={styles.dateIcon} />
            <Text style={[styles.infoValue, { color: colors.text }]}>{formatDate(item.expiresAt)}</Text>
          </View>
        </View>
      </View>

      <View style={styles.itemsContainer}>
        <Text style={[styles.sectionLabel, { color: colors.textSecondary }]}>
          {item.items.length} {item.items.length === 1 ? 'Item' : 'Items'}
        </Text>
      </View>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <View style={[styles.searchContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Search size={20} color={colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search estimates..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <Button
          title="Refresh"
          onPress={refetch}
          variant="outline"
          leftIcon={<RefreshCw size={16} color={colors.primary} />}
          disabled={loading}
          style={{ marginRight: 8 }}
        />

        <Button
          title="New"
          onPress={() => router.push('/estimates/create')}
          variant="primary"
          leftIcon={<Plus size={16} color="#FFFFFF" />}
        />
      </View>

      <View style={styles.filtersContainer}>
        {filterOptions.map(option => (
          <TouchableOpacity
            key={option.label}
            style={[
              styles.filterButton,
              {
                backgroundColor: filterStatus === option.value ? colors.primary : colors.card,
                borderColor: filterStatus === option.value ? colors.primary : colors.border,
              }
            ]}
            onPress={() => setFilterStatus(option.value)}
          >
            <Text
              style={[
                styles.filterButtonText,
                { color: filterStatus === option.value ? '#FFFFFF' : colors.text }
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading estimates...
          </Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={refetch}
            variant="outline"
            style={{ marginTop: 16 }}
          />
        </View>
      ) : (
        <FlatList
          data={filteredEstimates}
          renderItem={renderEstimateItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                No estimates found. Try adjusting your filters or create a new estimate.
              </Text>
              <Button
                title="Create New Estimate"
                onPress={() => router.push('/estimates/create')}
                variant="primary"
                style={{ marginTop: 16 }}
              />
            </View>
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  listContainer: {
    paddingBottom: 16,
  },
  estimateCard: {
    marginBottom: 12,
  },
  estimateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  estimateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    marginRight: 8,
  },
  clientSection: {
    marginBottom: 12,
  },
  sectionLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginBottom: 4,
  },
  clientName: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  infoColumn: {
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginRight: 4,
  },
  itemsContainer: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
});